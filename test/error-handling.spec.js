import { SELF } from 'cloudflare:test';
import { describe, it, expect } from 'vitest';
import { BASE_URL } from './setup';

// Test utilities
const api = {
  get: async (path) => {
    return SELF.fetch(`${BASE_URL}${path}`);
  },
  post: async (path, data) => {
    return SELF.fetch(`${BASE_URL}${path}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
};

describe('Error Handling', () => {
  it('returns 400 with error message for invalid JSON', async () => {
    try {
      const response = await SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: '{invalid-json',
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty('error');
      expect(data).toHaveProperty('errorId');
    } catch (error) {
      // If the error is caught by the fetch itself, verify it's a SyntaxError
      expect(error).toBeInstanceOf(SyntaxError);
      expect(error.message).toContain('JSON');
    }
  });

  it('returns 404 with error message for non-existent endpoint', async () => {
    const response = await api.get('/non-existent-endpoint');

    expect(response.status).toBe(404);
    expect(await response.text()).toBe('Not Found');
  });

  it('returns 400 with validation error for invalid prequalify data', async () => {
    const invalidData = {
      preQualifyFields: {
        // Missing most required fields
        fundingAmount: -100, // Invalid amount
      },
    };

    const response = await api.post('/app', invalidData);
    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Validation Error');
  });

  it('returns 400 with validation error for invalid application data', async () => {
    // First create a valid application
    const validPrequalData = {
      preQualifyFields: {
        fundingAmount: 75000,
        purpose: 'Expansion',
        topPriority: 'cost',
        timeline: 'month',
        businessName: 'Test Business LLC',
        monthlyRevenue: '************',
        businessStartDate: '2020-01-01',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '1234567890',
        estimatedFICO: '700-780',
        consent: true,
      },
    };

    const createResponse = await api.post('/app', validPrequalData);
    const createData = await createResponse.json();
    const appUuid = createData.uuid;

    // Now try to submit with invalid application data
    const invalidData = {
      applicationFields: {
        // Missing most required fields
        businessName: 'Test', // Valid but missing other required fields
      },
    };

    const response = await api.post(`/app/${appUuid}/submit`, invalidData);
    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Validation Error');
  });

  it('returns 404 for non-existent application UUID', async () => {
    const nonExistentUuid = 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee';
    const response = await api.get(`/app/${nonExistentUuid}`);

    expect(response.status).toBe(404);

    const data = await response.json();
    expect(data).toHaveProperty('error', expect.stringMatching(/^Application ".+" not found$/));
  });

  it('returns 400 when trying to sign an application that is not in APP_SUBMITTED status', async () => {
    // Create a new application but don't submit it
    const validPrequalData = {
      preQualifyFields: {
        fundingAmount: 75000,
        purpose: 'Expansion',
        topPriority: 'cost',
        timeline: 'month',
        businessName: 'Test Business LLC',
        monthlyRevenue: '************',
        businessStartDate: '2020-01-01',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '1234567890',
        estimatedFICO: '700-780',
        consent: true,
      },
    };

    const createResponse = await api.post('/app', validPrequalData);
    const createData = await createResponse.json();
    const appUuid = createData.uuid;

    // Try to sign it without submitting
    const response = await api.post(`/app/${appUuid}/sign`, {});

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain("Application can't be signed");
  });

  it('returns 400 when trying to complete an application that is not in APP_SIGNED status', async () => {
    // Create a new application but don't sign it
    const validPrequalData = {
      preQualifyFields: {
        fundingAmount: 75000,
        purpose: 'Expansion',
        topPriority: 'cost',
        timeline: 'month',
        businessName: 'Test Business LLC',
        monthlyRevenue: '************',
        businessStartDate: '2020-01-01',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '1234567890',
        estimatedFICO: '700-780',
        consent: true,
      },
      domain: 'app.pinnaclefunding.com',
    };

    const createResponse = await api.post('/app', validPrequalData);
    const createData = await createResponse.json();
    const appUuid = createData.uuid;

    // Try to complete it without signing
    const validApplicationData = {
      applicationFields: {
        businessName: 'Test Business LLC',
        entityType: 'LLC',
        ein: '123456789',
        industry: 'Technology',
        businessStartDate: '2020-01-01',
        businessPhone: '1234567890',
        businessEmail: '<EMAIL>',
        address: {
          line1: '123 Test St',
          city: 'Test City',
          state: 'NY',
          zip: '12345',
        },
        owners: [
          {
            firstName: 'Test',
            lastName: 'Owner',
            dateOfBirth: '1980-01-01',
            ssn: '123456789',
            email: '<EMAIL>',
            phone: '9876543210',
            address: {
              line1: '456 Owner St',
              city: 'Owner City',
              state: 'CA',
              zip: '54321',
            },
            ownershipPercentage: 100,
          },
        ],
      },
    };

    const response = await api.post(`/app/${appUuid}/complete`, validApplicationData);

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain("Application can't be completed");
  });
});
