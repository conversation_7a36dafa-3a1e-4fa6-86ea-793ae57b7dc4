import { sendEmailTemplate } from '../postmark';
import { getApplicationFromKV, getAppBankStatements } from '../helpers';
import { downloadDocumentWithoutSignature } from '../pandadoc';

const log = (...args) => console.log('ADMIN:\t', ...args);

function getEmojiFlag(countryCode) {
  if (!/^[a-zA-Z]{2}$/.test(countryCode)) {
    return '❓'; // Default fallback emoji for invalid input
  }
  const codePoints = countryCode
    ?.toUpperCase()
    .split('')
    .map((char) => 127397 + char.charCodeAt(0));
  return String.fromCodePoint(...codePoints);
}

function formatIpLocation({ country, city, region }) {
  const regionNames = new Intl.DisplayNames(['en'], { type: 'region' });
  const countryName = regionNames.of(country?.toUpperCase());

  if (country === 'US') {
    return `${city}, ${region}`;
  } else {
    return `${city || region}, ${countryName}`;
  }
}

export async function adminQueueHandler(message, env) {
  log('adminQueueHandler called');

  try {
    const { application } = message;

    if (!application) {
      console.error('No application data in message');
      return;
    }

    const appPortalUrl = application.domain ? `https://${application.domain}` : env.PORTAL_URL;

    log(`Application Status: ${application.status}`);

    // TODO: use a switch/case here
    if (['PREQUAL_APPROVED', 'PREQUAL_DENIED'].includes(application.status)) {
      log(`Processing prequal email to admin for application ${application.uuid}`);

      const recipientEmails = [env.ADMIN_EMAIL];
      if (application.agent?.email) recipientEmails.push(application.agent.email);

      const { country, city, region } = application.meta.initiated;
      application.location = `${getEmojiFlag(country)} ${formatIpLocation({ country, city, region })}`;
      application.completeUrl = `${appPortalUrl}/application/${application.uuid}`;
      application.preQualifyFields.fundingAmount = `$${application.preQualifyFields.fundingAmount.toLocaleString()}`;
      application.approvalAmount = application.approvalAmount ? `$${application.approvalAmount.toLocaleString()}` : undefined;
      const rawRevenue = application.preQualifyFields.monthlyRevenue;
      const formattedRevenue = rawRevenue
        ? rawRevenue
            .split(/([+-])/)
            .filter(Boolean)
            .map((part) => {
              if (/[+-]/.test(part)) return part; // keep + or -
              return `$${(+part).toLocaleString('en-US')}`;
            })
            .join('')
        : 'N/A';
      application.preQualifyFields.monthlyRevenue = formattedRevenue;

      const email = await sendEmailTemplate(env, {
        to: recipientEmails.join(','),
        template: env.ADMIN_PREQUAL_TEMPLATE,
        placeholders: application,
      });

      log(`Email result: ${JSON.stringify(email)}`);
    } else if (application.status == 'APP_COMPLETED') {
      const { uuid } = application;
      log(`Processing completed email to admin for application ${uuid}`);

      // fetching app from KV since queue message size is too large with the file attachments
      const app = await getApplicationFromKV(env, uuid);
      const storedBankStatements = await getAppBankStatements(env, uuid);

      const recipientEmails = [env.ADMIN_EMAIL];
      if (app.agent?.email) recipientEmails.push(app.agent.email);

      const { country, city, region } = app.meta.initiated;
      app.location = `${getEmojiFlag(country)} ${formatIpLocation({ country, city, region })}`;
      // asign fico from prequal to Owner #1 since we don't collect it on application
      app.applicationFields.owners[0].estimatedFICO = app.preQualifyFields.estimatedFICO;

      const attachments = storedBankStatements.map((file) => {
        const base64 = file.dataUrl.split(',')[1] || ''; // get base64 content after comma
        return {
          Name: file.name,
          Content: base64,
          ContentType: file.type || 'application/octet-stream',
        };
      });

      const signedAppBase64 = await downloadDocumentWithoutSignature(env, app.pandadoc.document.id);
      attachments.push({
        Name: `App - ${app.applicationFields.businessName}.pdf`,
        Content: signedAppBase64,
        ContentType: 'application/pdf',
      });

      const {
        applicationFields: { bankStatements, ...restFields },
        ...restApp
      } = app;

      const cleanedApp = { ...restApp, applicationFields: restFields };

      // TODO: make sure meta.completed object is available (right now it's using meta.signed)

      const email = await sendEmailTemplate(env, {
        to: recipientEmails.join(','),
        template: env.ADMIN_APP_COMPLETED_TEMPLATE,
        placeholders: cleanedApp,
        attachments: attachments,
      });

      log(`Email result: ${JSON.stringify(email)}`);
    } else if (application.status === 'PREQUAL_FAST_TRACK') {
      console.log('Processing fast track email to admin for application', application.uuid);

      const recipientEmails = [env.ADMIN_EMAIL];
      if (application.agent?.email) recipientEmails.push(application.agent.email);

      const { country, city, region } = application.meta.initiated;
      application.location = `${getEmojiFlag(country)} ${formatIpLocation({ country, city, region })}`;
      application.completeUrl = `${appPortalUrl}/application/${application.uuid}`;

      const email = await sendEmailTemplate(env, {
        to: recipientEmails.join(','),
        template: env.ADMIN_FASTTRACK_TEMPLATE,
        placeholders: application,
      });

      log(`Email result: ${JSON.stringify(email)}`);
    }
  } catch (error) {
    console.error(`Error processing email: ${error.message}`);
    throw error;
  }
}
