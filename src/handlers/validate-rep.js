import { createFactory } from 'hono/factory';
import { AppError, getAgentsList } from '../helpers';

const factory = createFactory();

export const validateRep = (agentsList, rep) => {
  if (rep.toLowerCase() === 'test') return true;
  const isValidRep = agentsList.some((agent) => {
    const emailPrefix = agent.email.split('@')[0];
    return emailPrefix.toLowerCase() === rep.toLowerCase();
  });

  return isValidRep;
};

export const validateRepHandlers = factory.createHandlers(async (c) => {
  const rep = c.req.param('rep')?.trim().toLowerCase();

  if (!rep) {
    throw new AppError('Missing rep parameter', 400, 'validationError');
  }

  const agentsList = await getAgentsList(c.env);
  console.log({ agentsList });

  const isValidRep = validateRep(agentsList, rep);

  return c.json({
    rep,
    valid: isValidRep,
  });
});
