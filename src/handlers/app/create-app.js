import { createFactory } from 'hono/factory';
import { validator } from 'hono/validator';
import { preQualifySchema } from '../../schema/prequal';
import { AppError, getAgentsList, safeUTMObject, updateApplicationInKV } from '../../helpers';
import { z } from 'zod';
import { getMeta, isPrequalApproved } from '../../utils';
import { getNextAgentRoundRobin } from '../../round-robin';
import { sendToEmailQueue, sendToSalesforceQueue, sendToAdminQueue } from '../../helpers/queue';

const factory = createFactory();

const customValidator = validator('json', (value, c) => {
  const schema = z.object({
    preQualifyFields: preQualifySchema,
    utm: z.object({}).passthrough().optional(),
    domain: z.enum(c.env.ALLOWED_DOMAINS),
  });
  const parsed = schema.safeParse(value);
  if (!parsed.success) {
    throw new AppError('Validation Error: Invalid request data', 400, 'validationError', parsed.error);
  }
  return parsed.data;
});

export const createtAppHandlers = factory.createHandlers(customValidator, async (c) => {
  const { preQualifyFields, utm, domain } = c.req.valid('json');

  // Trim all string values in preQualifyFields
  Object.keys(preQualifyFields).forEach((key) => {
    if (typeof preQualifyFields[key] === 'string') {
      preQualifyFields[key] = preQualifyFields[key].trim();
    }
  });

  const safeUtm = safeUTMObject(utm);

  const timestamp = c.get('timestamp');
  const uuid = crypto.randomUUID();

  const { prequalApproved, reason, approvalAmount } = isPrequalApproved(preQualifyFields);
  const status = prequalApproved ? 'PREQUAL_APPROVED' : 'PREQUAL_DENIED';

  let agent;
  const agentsList = await getAgentsList(c.env);
  const utmSource = utm?.utm_source?.trim().toLowerCase();
  const utmRep = utm?.utm_rep?.trim().toLowerCase();

  console.log({ utmSource, utmRep });

  const source = utmRep || utmSource;

  const preAssignedAgent =
    source === 'test'
      ? c.env.TEST_AGENT
      : agentsList.find((agent) => {
          const emailPrefix = agent.email.split('@')[0];
          return emailPrefix.toLowerCase() === source;
        });

  if (preAssignedAgent) {
    console.log({ preAssignedAgent });
    agent = preAssignedAgent;
  } else if (prequalApproved) {
    console.log('No agent, getting next from round robin');
    agent = await getNextAgentRoundRobin(c.env);
  }

  const application = {
    uuid,
    version: c.env.VERSION,
    status,
    created_at: timestamp,
    preQualifyFields: preQualifyFields,
    agent,
    utm: safeUtm,
    domain,
  };

  application.meta = { initiated: getMeta(c.req.raw, timestamp) };

  if (prequalApproved) {
    application.approvalAmount = approvalAmount;
  }

  if (!prequalApproved) {
    application.reason = reason;
  }

  await Promise.all([
    updateApplicationInKV(c.env, uuid, application, timestamp),
    sendToAdminQueue(c.env, { application }),
    sendToEmailQueue(c.env, { application }, { delaySeconds: c.env.EMAIL_QUEUE_DELAY }),
    sendToSalesforceQueue(c.env, { application }),
  ]);

  return c.json({ uuid, status, agent, reason, approvalAmount }, 201);
});
