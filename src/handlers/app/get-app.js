import { createFactory } from 'hono/factory';
import { AppError, cleanedApplication } from '../../helpers';
import { getApplicationFromKV } from '../../helpers';

const factory = createFactory();

export const ensureApplicationByUUID = async (c, next) => {
  const uuid = c.req.param('uuid');

  if (!uuid) {
    return c.json({ error: `Missing UUID` }, 400);
  }

  const application = await getApplicationFromKV(c.env, uuid);
  if (!application) {
    console.error(`Application "${uuid}" not found`);
    return c.json({ error: `Application "${uuid}" not found` }, 404);
  }

  c.set('application', application);
  console.log('App UUID:', uuid);
  await next();
};

export const getAppHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const application = c.get('application');
  return c.json({ data: cleanedApplication(application) });
});
