import { createFactory } from 'hono/factory';
import { ensureApplicationByUUID } from './get-app';
import { AppError, cleanedApplication, updateApplicationInKV } from '../../helpers';
import { moveDocumentToDraft } from '../../pandadoc';

const factory = createFactory();

export const editAppHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const timestamp = c.get('timestamp');
  const application = c.get('application');

  if (application.status === 'APP_SUBMITTED') {
    application.status = 'APP_EDITING';
    const document = await moveDocumentToDraft(c.env, application.pandadoc?.document?.id);
    application.pandadoc = { document };
    await updateApplicationInKV(c.env, application.uuid, application, timestamp);

    return c.json({ data: cleanedApplication(application) });
  } else {
    console.error(`Application status ${application.status} can't be edited`);
    throw new AppError(`Application can't be edited (${application.status})`, 400, 'editApp');
  }
});
